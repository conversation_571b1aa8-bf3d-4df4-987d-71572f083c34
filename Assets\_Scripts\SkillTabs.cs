using System.Collections;
using System.Collections.Generic;
using UnityEngine;

public class SkillTabs : MonoBehaviour
{
    [SerializeField] private RectTransform rotationCenter;
    [SerializeField] private RectTransform[] Placeholders;
    [SerializeField] public RectTransform[] Tabs;

    float minScale = 0.6f;
    private int middleIndex;
    Vector2[] originalPositions;
    Vector3[] originalScales;

    void Awake()
    {
        originalPositions = new Vector2[Tabs.Length];
        originalScales = new Vector3[Tabs.Length];

        for (int i = 0; i < Tabs.Length; i++)
        {
            originalPositions[i] = Tabs[i].anchoredPosition;
            originalScales[i] = Tabs[i].localScale;
        }

        middleIndex = Tabs.Length / 2;
    }

    public void InitializeTabs(float duration = 0.3f)
    {
        float scaleStep = (1.0f - minScale) / middleIndex;

        for (int i = 0; i < Tabs.Length; i++)
        {
            int distanceFromMiddle = Mathf.Abs(i - middleIndex);
            float scale = 1.0f - (distanceFromMiddle * scaleStep);

            Vector2 targetPos = Placeholders[i].anchoredPosition;
            StartCoroutine(LerpTab(Tabs[i], targetPos, new Vector3(scale, scale, 1f), duration));
        }
    }

    public void OnTabClicked(int clickedIndex, float duration = 0.3f)
    {
        if (clickedIndex == middleIndex)
            return;

        int shiftAmount = middleIndex - clickedIndex;

        Tabs = RotateArray(Tabs, shiftAmount);

        InitializeTabs(duration);
    }

    public void ResetTabs(float duration = 0.3f)
    {
        for (int i = 0; i < Tabs.Length; i++)
        {
            StartCoroutine(LerpTab(Tabs[i], originalPositions[i], originalScales[i], duration));
        }
    }

    IEnumerator LerpTab(RectTransform tab, Vector2 targetPos, Vector3 targetScale, float duration)
    {
        Vector2 startPos = tab.anchoredPosition;
        Vector3 startScale = tab.localScale;
        Vector2 center = rotationCenter.localPosition;

        // Debug.Log("startPos: " + startPos + " targetPos: " + targetPos);

        // Convert start and end positions to polar coords
        float startAngle = Mathf.Atan2(startPos.y - center.y, startPos.x - center.x);
        float endAngle = Mathf.Atan2(targetPos.y - center.y, targetPos.x - center.x);

        // Debug.Log("startAngle: " + startAngle + " endAngle: " + endAngle);

        float startRadius = Vector2.Distance(startPos, center);
        float endRadius = Vector2.Distance(targetPos, center);

        Debug.Log("startRadius: " + startRadius + " endRadius: " + endRadius);

        // find shortest rotation
        float angleDiff = Mathf.DeltaAngle(Mathf.Rad2Deg * startAngle, Mathf.Rad2Deg * endAngle);
        // float angleDiff = (Mathf.Rad2Deg * endAngle - Mathf.Rad2Deg * startAngle + 360f) % 360f;
        float angleDiffRad = angleDiff * Mathf.Deg2Rad;

        // Debug.Log("angleDiff: " + angleDiff + " angleDiffRad: " + angleDiffRad);

        float time = 0f;

        while (time < duration)
        {
            float t = time / duration;
            float angle = startAngle + angleDiffRad * t;
            float radius = Mathf.Lerp(startRadius, endRadius, t);

            float x = center.x + Mathf.Cos(angle) * radius;
            float y = center.y + Mathf.Sin(angle) * radius;

            tab.anchoredPosition = new Vector2(x, y);
            tab.localScale = Vector3.Lerp(startScale, targetScale, t);

            time += Time.deltaTime;
            yield return null;
        }

        // snap to target
        tab.anchoredPosition = targetPos;
        tab.localScale = targetScale;
    }

    public void TabClicked(int index)
    {
        OnTabClicked(index);
    }

    private T[] RotateArray<T>(T[] array, int shiftAmount)
    {
        int n = array.Length;
        T[] rotated = new T[n];

        for (int i = 0; i < n; i++)
        {
            int newIndex = (i + shiftAmount + n) % n;
            rotated[newIndex] = array[i];
        }

        return rotated;
    }

    void OnDrawGizmos()
    {
        if (Placeholders == null || rotationCenter == null) return;

        Gizmos.color = Color.red;
        Gizmos.DrawSphere(rotationCenter.position, 10f);

        Gizmos.color = Color.green;
        foreach (var ph in Placeholders)
        {
            Gizmos.DrawLine(rotationCenter.position, ph.position);
            Gizmos.DrawSphere(ph.position, 5f);
        }
    }

}
